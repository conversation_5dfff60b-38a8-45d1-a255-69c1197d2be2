# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a Salesforce Marketing Cloud ETL integration system that extracts data from multiple sources (NewCon SQL Server, RD Station API, Orbbits MySQL) and loads it into Salesforce Marketing Cloud Data Extensions. The system is designed for high performance with parallel processing and includes both Airflow DAG orchestration and standalone execution capabilities.

### Key Architecture Components

- **Parallel ETL Pipeline**: 9 source tables extracted simultaneously, transformed in parallel, and loaded to 4 Data Extensions
- **Multi-source Integration**: SQL Server (NewCon), PostgreSQL (DW), MySQL (Orbbits), RD Station API
- **Salesforce Marketing Cloud**: Target destination with 4 Data Extensions (tb_produtos, tb_clientes, tb_leads, tb_propostas)
- **Airflow Integration**: Complete DAG orchestration with task dependencies and parallel execution
- **Standalone Execution**: Direct Python execution with command-line arguments for testing and manual runs

## Common Development Commands

### Running the ETL Pipeline

```bash
# Navigate to project directory
cd /home/<USER>/projects/airflow-v1/dags/salesforce_integration

# Install dependencies
pip install -r requirements.txt

# Full pipeline execution (production mode)
export SALESFORCE_OUTPUT_MODE=salesforce
python etl_main.py

# Test mode (generates CSV files)
export SALESFORCE_OUTPUT_MODE=csv
python etl_main.py

# Dry run (validation only)
python etl_main.py --dry-run

# Test with limited sample
python etl_main.py --test-sample 1000

# Run specific tables only
python etl_main.py --tables produtos,clientes

# Exclude specific tables
python etl_main.py --exclude propostas
```

### Airflow Operations

```bash
# Activate DAG
airflow dags unpause etl_salesforce_marketing_cloud_table_parallel

# Trigger manual execution
airflow dags trigger etl_salesforce_marketing_cloud_table_parallel

# Check DAG status
airflow dags state etl_salesforce_marketing_cloud_table_parallel $(date +%Y-%m-%d)

# List recent runs
airflow dags list-runs -d etl_salesforce_marketing_cloud_table_parallel
```

### Testing and Validation

```bash
# Test all database connections
python -c "from database_connections import test_all_connections; test_all_connections()"

# Validate configuration
python config.py

# Test individual components
python data_extractors.py
python data_transformers.py
python salesforce_client.py
```

### Development and Debugging

```bash
# Enable debug mode
export DEBUG=true
export LOG_LEVEL=DEBUG

# Run with progress monitoring
export MONITORING_PROGRESS_BAR=true

# Generate detailed logs
tail -f logs/etl_consolidated.log
```

## Key Configuration Files

- **config.py**: Centralized configuration for all database connections, Salesforce settings, batch sizes, and operational parameters
- **requirements.txt**: Complete dependency specification with version pinning
- **dag_etl_salesforce_table_parallel.py**: Airflow DAG definition with parallel task execution

## Data Flow Architecture

### Source Systems
- **NewCon (SQL Server)**: Primary CRM data (clients, products, leads, proposals)
- **RD Station (API)**: Marketing leads and campaign data
- **Orbbits (MySQL)**: Additional business data (origin, payments, sales, prices)
- **DW Corporativo (PostgreSQL)**: Data warehouse for some transformations

### Target System
- **Salesforce Marketing Cloud**: 4 Data Extensions with specific external keys
  - tb_produtos: FCC1DCA7-D286-458D-BDCC-D050C1BA61A8
  - tb_clientes: B2B7ACFF-D5C8-4C86-B04A-DB4FBBA9198E
  - tb_leads: EC0B7BFF-EC89-4A4D-914B-749F14B6F861
  - tb_propostas: 36B6F6B2-ECC3-4550-828C-5BF3B12FCBCA

### Processing Layers
1. **Extraction**: Parallel extraction from 9 source tables
2. **Transformation**: Data consolidation and business rule application (4 final tables)
3. **Loading**: Parallel batch upload to Salesforce Marketing Cloud (2000 records per batch)

## Environment Variables

### Required Environment Variables
```bash
# Database Connections
export NEWCON_USER="username"
export NEWCON_PASSWORD="password"
export DW_USER="username"
export DW_PASSWORD="password"
export ORBBITS_USER="username"
export ORBBITS_PASSWORD="password"

# Salesforce Marketing Cloud
export SALESFORCE_CLIENT_ID="client_id"
export SALESFORCE_CLIENT_SECRET="client_secret"

# RD Station
export RDSTATION_TOKEN="api_token"
```

### Optional Configuration
```bash
# Operation modes
export SALESFORCE_OUTPUT_MODE=csv|salesforce
export SALESFORCE_VERIFY_STATUS=true|false
export DRY_RUN=true|false
export DEBUG=true|false

# Performance tuning
export MAX_WORKERS=4
export SALESFORCE_BATCH_SIZE=2000
export PARALLEL_EXECUTION=true
```

## Key Modules and Their Purposes

- **etl_main.py**: Main orchestrator with command-line interface and pipeline execution
- **data_extractors.py**: Handles all source system extractions with connection pooling
- **data_transformers.py**: Business logic and data transformation rules
- **salesforce_client.py**: Salesforce Marketing Cloud API integration with batch processing
- **database_connections.py**: Database connection management with failover support
- **utils.py**: Logging, progress tracking, and utility functions
- **config.py**: Centralized configuration management with environment variable support

## Error Handling and Debugging

### Common Issues
- **Connection failures**: Check database connectivity and credentials
- **API rate limits**: RD Station (180 req/min), Salesforce (200 req/min)
- **Memory issues**: Large datasets may require batch size adjustment
- **Timeout errors**: Increase timeout values in config.py

### Log Analysis
- Main log file: `logs/etl_consolidated.log`
- Structured logging with JSON format available
- Progress tracking with real-time metrics
- Individual component logs for detailed debugging

## Performance Characteristics

### Expected Execution Times
- **Full pipeline**: 30-55 minutes (~630k records)
- **Extraction phase**: 15-25 minutes (9 parallel tables)
- **Transformation phase**: 5-10 minutes (4 parallel processes)
- **Loading phase**: 10-20 minutes (4 parallel Data Extensions)

### Volume Expectations
- tb_produtos: ~20k records
- tb_clientes: ~73k records
- tb_leads: Variable volume
- tb_propostas: ~533k records

## Development Guidelines

- All database connections use connection pooling and automatic retry
- Batch processing is optimized for 2000 records per Salesforce batch
- Parallel execution is the default for maximum performance
- Configuration is externalized via environment variables
- Comprehensive logging and progress tracking is built-in
- Both Airflow and standalone execution modes are supported