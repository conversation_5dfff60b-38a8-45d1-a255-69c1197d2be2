"""
ETL Consolidado - Extração de Dados
Todas as extrações de dados consolidadas com rate limiting, retry automático e paginação.
"""

import time
import logging
import pandas as pd
from datetime import datetime
from typing import Dict, List
from threading import Lock
from rdstation.crm import CRMClient
from salesforce_integration.config import *
from salesforce_integration.database_connections import get_database_connection
from salesforce_integration.utils import (
    retry_decorator,
    ProgressTracker,
    DataQualityChecker,
    setup_logging
)

# =============================================================================
# CONFIGURAÇÃO DE LOGGING
# =============================================================================

logger = setup_logging()

# =============================================================================
# CLASSE PRINCIPAL DE EXTRAÇÃO
# =============================================================================

class DataExtractor:
    """Classe principal para extrair dados de todas as fontes"""
    
    def __init__(self, test_sample: int = None):
        self.logger = logging.getLogger(__name__)
        self.test_sample = test_sample
        self.extraction_stats = {
            'start_time': None,
            'end_time': None,
            'total_records': 0,
            'sources': {}
        }
        self._rate_limit_lock = Lock()
        self._last_rdstation_request = 0

        if test_sample:
            self.logger.info(f"Modo teste ativado: limitando consultas a {test_sample} registros")
        
    def extract_all_sources(self, sources: List[str] = None) -> Dict[str, pd.DataFrame]:
        """Extrai dados de todas as fontes ou fontes específicas"""
        self.extraction_stats['start_time'] = datetime.now()
        
        if sources is None:
            sources = ['newcon', 'rdstation', 'orbbits']
        
        self.logger.info(f"Iniciando extração de dados das fontes: {', '.join(sources)}")
        
        results = {}
        
        # Extração sequencial para evitar sobrecarga
        for source in sources:
            try:
                self.logger.info(f"Extraindo dados da fonte: {source}")
                
                if source == 'newcon':
                    results.update(self._extract_newcon_all())
                elif source == 'rdstation':
                    results['rdstation_leads'] = self.extract_rdstation_leads()
                elif source == 'orbbits':
                    results.update(self._extract_orbbits_all())
                
                self.logger.info(f"✅ Extração de {source} concluída com sucesso")
                
            except Exception as e:
                self.logger.error(f"❌ Erro na extração de {source}: {e}")
                # Continua com outras fontes em caso de erro
                continue
        
        self.extraction_stats['end_time'] = datetime.now()
        self.extraction_stats['total_records'] = sum(
            len(df) for df in results.values() if isinstance(df, pd.DataFrame)
        )
        
        self._log_extraction_summary(results)
        
        return results

    # Métodos de extração específicos para compatibilidade
    def extract_newcon_clients(self) -> pd.DataFrame:
        """Extrai clientes do NewCon"""
        extractor = NewConExtractor(test_sample=self.test_sample)
        return extractor.extract_clients()

    def extract_newcon_leads(self) -> pd.DataFrame:
        """Extrai leads do NewCon"""
        extractor = NewConExtractor(test_sample=self.test_sample)
        return extractor.extract_leads()

    def extract_newcon_products(self) -> pd.DataFrame:
        """Extrai produtos do NewCon"""
        extractor = NewConExtractor(test_sample=self.test_sample)
        return extractor.extract_products()

    def extract_newcon_proposals(self) -> pd.DataFrame:
        """Extrai propostas do NewCon"""
        extractor = NewConExtractor(test_sample=self.test_sample)
        return extractor.extract_proposals()

    def extract_rdstation_leads(self) -> pd.DataFrame:
        """Extrai leads do RD Station"""
        extractor = RDStationExtractor(test_sample=self.test_sample)
        return extractor.extract_leads()

    def extract_orbbits_origin(self) -> pd.DataFrame:
        """Extrai dados de origem do Orbbits"""
        extractor = OrbbitsExtractor(test_sample=self.test_sample)
        return extractor.extract_origin()

    def extract_orbbits_payments(self) -> pd.DataFrame:
        """Extrai dados de pagamentos do Orbbits"""
        extractor = OrbbitsExtractor(test_sample=self.test_sample)
        return extractor.extract_payments()

    def extract_orbbits_sales(self) -> pd.DataFrame:
        """Extrai dados de vendas do Orbbits"""
        extractor = OrbbitsExtractor(test_sample=self.test_sample)
        return extractor.extract_sales()

    def extract_orbbits_prices(self) -> pd.DataFrame:
        """Extrai dados de preços do Orbbits"""
        extractor = OrbbitsExtractor(test_sample=self.test_sample)
        return extractor.extract_prices()

    def _extract_newcon_all(self) -> Dict[str, pd.DataFrame]:
        """Extrai todos os dados do NewCon"""
        results = {}
        
        # Extrai cada tabela do NewCon
        extractors = {
            'newcon_clients': self.extract_newcon_clients,
            'newcon_leads': self.extract_newcon_leads,
            'newcon_products': self.extract_newcon_products,
            'newcon_proposals': self.extract_newcon_proposals
        }
        
        for key, extractor in extractors.items():
            try:
                results[key] = extractor()
                self.logger.info(f"✅ {key}: {len(results[key])} registros extraídos")
            except Exception as e:
                self.logger.error(f"❌ Erro em {key}: {e}")
                results[key] = pd.DataFrame()  # DataFrame vazio em caso de erro
        
        return results
    
    def _extract_orbbits_all(self) -> Dict[str, pd.DataFrame]:
        """Extrai todos os dados do Orbbits"""
        results = {}
        
        # Extrai cada tabela do Orbbits
        extractors = {
            'orbbits_origin': self.extract_orbbits_origin,
            'orbbits_payments': self.extract_orbbits_payments,
            'orbbits_sales': self.extract_orbbits_sales,
            'orbbits_prices': self.extract_orbbits_prices
        }
        
        for key, extractor in extractors.items():
            try:
                results[key] = extractor()
                self.logger.info(f"✅ {key}: {len(results[key])} registros extraídos")
            except Exception as e:
                self.logger.error(f"❌ Erro em {key}: {e}")
                results[key] = pd.DataFrame()  # DataFrame vazio em caso de erro
        
        return results
    
    def _log_extraction_summary(self, results: Dict[str, pd.DataFrame]):
        """Registra resumo da extração"""
        duration = self.extraction_stats['end_time'] - self.extraction_stats['start_time']
        
        self.logger.info("=" * 60)
        self.logger.info("RESUMO DA EXTRAÇÃO")
        self.logger.info("=" * 60)
        self.logger.info(f"Duração total: {duration}")
        self.logger.info(f"Total de registros: {self.extraction_stats['total_records']:,}")
        
        for source, df in results.items():
            if isinstance(df, pd.DataFrame):
                self.logger.info(f"- {source}: {len(df):,} registros")
        
        self.logger.info("=" * 60)

# =============================================================================
# EXTRAÇÃO NEWCON (SQL SERVER)
# =============================================================================

class NewConExtractor:
    """Extrator especializado para NewCon"""
    
    def __init__(self, test_sample: int = None):
        self.logger = logging.getLogger(__name__)
        self.db_name = 'newcon'
        self.test_sample = test_sample
    
    @retry_decorator(max_attempts=3, delay=2)
    def extract_clients(self) -> pd.DataFrame:
        """Extrai clientes do NewCon"""
        self.logger.info("Extraindo clientes do NewCon...")
        
        query = """
        SELECT
            'Clientes' as end_point,
            concc036.id_documento,
            corcc023.cd_inscricao_nacional as CNPJCPF,
            LEFT(corcc023.nm_pessoa, CHARINDEX(' ', corcc023.nm_pessoa + ' ') - 1) as PrimeiroNome,
            corcc023.nm_pessoa as NomeCompleto,
            ddd_endereco.nm_cidade as Cidade,
            ddd_endereco.id_uf as Estado,
            CAST(corcc023a.dt_nascimento as date) as dt_nascimento,
            CONCAT('(55', ISNULL(ddd_telefone.ddd, ddd_endereco.ddd), corcc027.telefone, ')') as Celular,
            'br' as Locale,
            corcc030.e_mail,
            corcc023a.st_sexo as Genero,
            conve001.nm_fantasia as PontoVenda,
            CAST(corcc023a.dh_inclusao as date) as dt_cadastro,
            CAST(corcc023a.vl_renda as numeric(18,2)) as Renda,
            CASE 
                WHEN corcc023.sn_politicamente_exposto = 'S' THEN 1
                ELSE 0
            END as politicamente_exposto,
            NULL as Optin_seguros_email,
            NULL as Optin_seguros_SMS,
            NULL as Optin_seguros_wpp,
            NULL as Optin_capital_email,
            NULL as Optin_capital_SMS,
            NULL as Optin_capital_whatsapp,
            NULL as Campaign,
            NULL as Source,
            NULL as Medium,
            NULL as Term,
            NULL as Content,
            1 as cliente_consorcio,
            NULL as cliente_seguros,
            CASE 
                WHEN conve001.id_ponto_venda = 259 THEN 1
                ELSE 0
            END as cliente_digital,
            NULL as cliente_capital
        FROM corcc023
        INNER JOIN (
            SELECT MAX(id_cota) as id_cota, id_pessoa
            FROM conve002 
            WHERE versao = 0
            GROUP BY id_pessoa
        ) ult_venda ON ult_venda.id_pessoa = corcc023.id_pessoa
        INNER JOIN conve002 ON conve002.id_cota = ult_venda.id_cota
        INNER JOIN conve001 ON conve001.id_ponto_venda = conve002.id_ponto_venda
        LEFT JOIN corcc023a ON corcc023a.id_pessoa = corcc023.id_pessoa
        INNER JOIN corcc030 ON corcc030.id_pessoa = conve002.id_pessoa 
            AND corcc030.id_e_mail = conve002.id_e_mail
        INNER JOIN corcc026 ON corcc026.id_pessoa = conve002.id_pessoa 
            AND corcc026.id_endereco = conve002.id_endereco
        INNER JOIN corcc027 ON corcc027.id_pessoa = conve002.id_pessoa 
            AND corcc027.id_telefone = conve002.id_telefone
        INNER JOIN corcc015 ddd_endereco ON ddd_endereco.id_cidade = corcc026.id_cidade
        INNER JOIN corcc015 ddd_telefone ON ddd_telefone.id_cidade = corcc027.id_cidade
        INNER JOIN concc036 ON concc036.id_cota = conve002.id_cota
        """

        # Adiciona LIMIT se test_sample estiver definido
        if self.test_sample:
            query += f"\nORDER BY corcc023.id_pessoa\nOFFSET 0 ROWS FETCH NEXT {self.test_sample} ROWS ONLY"

        try:
            with get_database_connection(self.db_name) as conn:
                df = pd.read_sql(query, conn)
                
                # Processa dados
                df['id_documento'] = df['id_documento'].astype(str)
                
                # Validação de qualidade
                self._validate_client_data(df)
                
                self.logger.info(f"✅ {len(df)} clientes extraídos do NewCon")
                return df
                
        except Exception as e:
            self.logger.error(f"❌ Erro ao extrair clientes do NewCon: {e}")
            raise
    
    @retry_decorator(max_attempts=3, delay=2)
    def extract_leads(self) -> pd.DataFrame:
        """Extrai leads do NewCon"""
        self.logger.info("Extraindo leads do NewCon...")

        # Query simplificada para teste
        if self.test_sample:
            query = """
            SELECT TOP 50
                'Leads' as end_point,
                concc030.id_documento,
                corcc023.cd_inscricao_nacional as CNPJCPF,
                '<EMAIL>' as Email,
                CAST(concc030.dt_cadastro as date) as Dt_cadastro,
                'br' as Locale,
                LEFT(corcc023.nm_pessoa, CHARINDEX(' ', corcc023.nm_pessoa + ' ') - 1) as PrimeiroNome,
                corcc023.nm_pessoa as NomeCompleto,
                'Bamaq Consórcio' as TipoEmpresa,
                'Consórcio' as TipoSimulacao,
                CAST(concc030.vl_bem as numeric(18,2)) as ValorSimulacao,
                NULL as Campaign,
                NULL as Source,
                NULL as Medium,
                NULL as Term,
                NULL as Content,
                NULL as PontoVenda,
                NULL as PlanoVenda,
                NULL as dt_nascimento,
                NULL as Optin_seguros_email,
                NULL as Optin_seguros_SMS,
                NULL as Optin_seguros_wpp,
                NULL as Optin_capital_email,
                NULL as Optin_capital_SMS,
                NULL as Optin_capital_whatsapp,
                CAST(concc030.dh_inclusao as date) as Dt_simulacao,
                NULL as NomeVendedor,
                NULL as Celular,
                NULL as Cidade,
                NULL as Estado,
                NULL as TipoBem
            FROM concc030
            INNER JOIN corcc023 ON corcc023.id_pessoa = concc030.id_pessoa
            WHERE concc030.dt_cadastro IS NOT NULL
            """
        else:
            query = """
      with propostas_nao_alocadas as (
            select
                'Leads' end_point
                ,concc030.id_documento
                ,corcc023.cd_inscricao_nacional CNPJCPF
                ,corcc030.e_mail Email
                ,cast(concc030.dt_cadastro as date) Dt_cadastro
                ,concat('(55', isnull(ddd_telefone.ddd, ddd_endereco.ddd), corcc027.telefone, ')') Celular
                ,'br' Locale
                ,left(corcc023.nm_pessoa, charindex(' ', corcc023.nm_pessoa + ' ') - 1) PrimeiroNome
                ,corcc023.nm_pessoa NomeCompleto
                ,ddd_endereco.nm_cidade Cidade
                ,ddd_endereco.id_uf Estado
                ,'Bamaq Consórcio' TipoEmpresa
                ,conbe003.nm_produto as TipoBem
                ,'Consórcio' TipoSimulacao
                ,cast(concc030.vl_bem as numeric(18,2)) ValorSimulacao
                ,null Campaign
                ,null Source
                ,null Medium
                ,null Term
                ,null Content
                ,conve001.nm_fantasia PontoVenda
                ,conve041.nm_plano_venda PlanoVenda
                ,cast(corcc023a.dt_nascimento as date) dt_nascimento
                ,null Optin_seguros_email
                ,null Optin_seguros_SMS
                ,null Optin_seguros_wpp
                ,null Optin_capital_email
                ,null Optin_capital_SMS
                ,null Optin_capital_whatsapp
                ,cast(concc030.dh_inclusao as date) Dt_simulacao
                ,conve014.nm_fantasia NomeVendedor
            from
                concc030
                inner join
                    corcc023 on
                    corcc023.id_pessoa = concc030.id_pessoa
                left join
                    corcc023a on
                    corcc023a.id_pessoa = corcc023.id_pessoa
                left join
                    concc036 on
                    concc036.id_empresa = concc030.id_empresa
                    and concc036.id_tipo_documento = concc030.id_tipo_documento
                    and concc036.id_documento = concc030.id_documento
                left join
                    corcc030 on
                    corcc030.id_pessoa = concc030.id_pessoa
                    and corcc030.id_e_mail = concc030.id_e_mail
                inner join
                    corcc026 on
                    corcc026.id_pessoa = concc030.id_pessoa
                    and corcc026.id_endereco = concc030.id_endereco
                inner join
                    corcc027 on
                    corcc027.id_pessoa = concc030.id_pessoa
                    and corcc027.id_telefone = concc030.id_telefone
                inner join
                    corcc015 ddd_endereco on
                    ddd_endereco.id_cidade = corcc026.id_cidade
                inner join
                    corcc015 ddd_telefone on
                    ddd_telefone.id_cidade = corcc027.id_cidade
                inner join
                    conbe007 on
                    conbe007.id_bem = concc030.id_bem
                inner join
                    conve001 on
                    conve001.id_ponto_venda = concc030.id_ponto_venda
                inner join
                    conve041 on
                    conve041.id_plano_venda = concc030.id_plano_venda
                inner join
                    conve014 on
                    conve014.id_comissionado = concc030.id_comissionado
    left join  
     conbe023 (NOLOCK) ON 
     conbe007.ID_CONBE023 = conbe023.ID_CONBE023
    left join 
    conbe003 (NOLOCK) ON 
    conbe023.ID_Produto = conbe003.ID_Produto
            where
                concc036.id_cota is null
    and datediff (dd, cast(concc030.dt_cadastro as date) , cast(getdate() as date)) <=30
        ),

        vendas as (
            select
                'Leads' end_point
                ,concc036.id_documento
                ,corcc023.cd_inscricao_nacional CNPJCPF
                ,corcc030.e_mail Email
                ,conve002d.dt_cadastro Dt_cadastro
                ,concat('(55', isnull(ddd_telefone.ddd, ddd_endereco.ddd), corcc027.telefone, ')') Celular
                ,'br' Locale
                ,left(corcc023.nm_pessoa, charindex(' ', corcc023.nm_pessoa + ' ') - 1) PrimeiroNome
                ,corcc023.nm_pessoa NomeCompleto
                ,ddd_endereco.nm_cidade Cidade
                ,ddd_endereco.id_uf Estado
                ,'Bamaq Consórcio' TipoEmpresa
    ,conbe003.nm_produto as TipoBem
                ,'Consórcio' TipoSimulacao
                ,conve002d.vl_credito ValorSimulacao
                ,null Campaign
                ,null Source
                ,null Medium
                ,null Term
                ,null Content
                ,conve001.nm_fantasia PontoVenda
                ,conve041.nm_plano_venda PlanoVenda
                ,corcc023a.dt_nascimento
                ,null Optin_seguros_email
                ,null Optin_seguros_SMS
                ,null Optin_seguros_wpp
                ,null Optin_capital_email
                ,null Optin_capital_SMS
                ,null Optin_capital_whatsapp
                ,concc030.dh_inclusao Dt_simulacao
                ,conve014.nm_fantasia NomeVendedor
            from
                conve002
                inner join
                    conve002d on
                    conve002d.id_cota = conve002.id_cota
                left join
                    concc036 on
                    concc036.id_cota = conve002.id_cota
                left join
                    concc030 on
                    concc030.id_empresa = concc036.id_empresa
                    and concc030.id_tipo_documento = concc036.id_tipo_documento
                    and concc030.id_documento = concc036.id_documento
                inner join
                    corcc023 on
                    corcc023.id_pessoa = conve002.id_pessoa
                left join
                    corcc023a on
                    corcc023a.id_pessoa = corcc023.id_pessoa
                inner join
                    corcc030 on
                    corcc030.id_pessoa = conve002.id_pessoa
                    and corcc030.id_e_mail = conve002.id_e_mail
                inner join
                    corcc026 on
                    corcc026.id_pessoa = conve002.id_pessoa
                    and corcc026.id_endereco = conve002.id_endereco
                inner join
                    corcc027 on
                    corcc027.id_pessoa = conve002.id_pessoa
                    and corcc027.id_telefone = conve002.id_telefone
                inner join
                    corcc015 ddd_endereco on
                    ddd_endereco.id_cidade = corcc026.id_cidade
                inner join
                    corcc015 ddd_telefone on
                    ddd_telefone.id_cidade = corcc027.id_cidade
                inner join
                    conbe007 on
                    conbe007.id_bem = conve002d.id_bem
                inner join
                    conve001 on
                    conve001.id_ponto_venda = conve002.id_ponto_venda
                inner join
                    conve041 on
                    conve041.id_plano_venda = conve002.id_plano_venda
                inner join
                    conve014 on
                    conve014.id_comissionado = conve002.id_comissionado
    left join  
     conbe023 (NOLOCK) ON 
     conbe007.ID_CONBE023 = conbe023.ID_CONBE023
    left join 
    conbe003 (NOLOCK) ON 
    conbe023.ID_Produto = conbe003.ID_Produto
   where datediff (dd, cast(conve002d.dt_cadastro as date) , cast(getdate() as date)) <=30
        ),

        final as(
            select * from propostas_nao_alocadas
            union all
            select * from vendas
        )
        select * from final
        """

            # Adiciona LIMIT se test_sample estiver definido (apenas para query completa)
            if self.test_sample:
                query += f"\nORDER BY id_documento\nOFFSET 0 ROWS FETCH NEXT {self.test_sample} ROWS ONLY"

        try:
            with get_database_connection(self.db_name) as conn:
                df = pd.read_sql(query, conn)
                
                # Processa dados
                df['id_documento'] = df['id_documento'].astype(str)
                
                # Validação de qualidade
                self._validate_lead_data(df)
                
                self.logger.info(f"✅ {len(df)} leads extraídos do NewCon")
                return df
                
        except Exception as e:
            self.logger.error(f"❌ Erro ao extrair leads do NewCon: {e}")
            raise
    
    @retry_decorator(max_attempts=3, delay=2)
    def extract_products(self) -> pd.DataFrame:
        """Extrai produtos do NewCon"""
        self.logger.info("Extraindo produtos do NewCon...")
        
        query = """
        SELECT
            'Produtos' as end_point,
            vw_grproduto.id_grupo,
            vw_grproduto.id_plano_venda,
            vw_grproduto.id_taxa_plano,
            vw_grproduto.id_produto,
            conbi008a.id_bem,
            vw_grproduto.cd_grupo as grupo,
            conbe007.nm_bem as descricao,
            vw_grproduto.nm_produto as tipo_bem,
            'Consórcio' as categoria,
            CAST(conbi008a.vl_bem as numeric(18,2)) as valor_credito,
            NULL as valor_parc1,
            NULL as valor_parc1_com_seguro,
            NULL as valor_demais_parc,
            NULL as valor_demais_parc_com_seguro,
            vw_grproduto.pz_comercializacao as qtd_parcelas,
            conve041.pe_contemplacao as reducao_contemplacao,
            vw_grproduto.pe_ta as taxa_admin,
            vw_grproduto.pe_fr as fundo_reserva,
            CAST(conbe015a.dt_reajuste as date) as dt_atualizacao
        FROM vw_grproduto
        INNER JOIN conbi008a ON conbi008a.id_plano_venda = vw_grproduto.id_plano_venda
            AND conbi008a.id_taxa_plano = vw_grproduto.id_taxa_plano
            AND conbi008a.id_produto = vw_grproduto.id_produto
        INNER JOIN conbe007 ON conbe007.id_bem = conbi008a.id_bem
        INNER JOIN conve041 ON conve041.id_plano_venda = vw_grproduto.id_plano_venda
        LEFT JOIN (
            SELECT id_bem, MAX(dt_ht_conbe015) as dt_reajuste
            FROM conbe015a
            GROUP BY id_bem, id_regiao_fiscal
        ) conbe015a ON conbe015a.id_bem = conbi008a.id_bem
        """

        # Adiciona LIMIT se test_sample estiver definido
        if self.test_sample:
            query += f"\nORDER BY vw_grproduto.id_grupo\nOFFSET 0 ROWS FETCH NEXT {self.test_sample} ROWS ONLY"

        try:
            with get_database_connection(self.db_name) as conn:
                df = pd.read_sql(query, conn)
                
                # Validação de qualidade
                self._validate_product_data(df)
                
                self.logger.info(f"✅ {len(df)} produtos extraídos do NewCon")
                return df
                
        except Exception as e:
            self.logger.error(f"❌ Erro ao extrair produtos do NewCon: {e}")
            raise
    
    @retry_decorator(max_attempts=3, delay=2)
    def extract_proposals(self) -> pd.DataFrame:
        """Extrai propostas do NewCon"""
        self.logger.info("Extraindo propostas do NewCon...")

        query = """
            -- PROPOSTAS
                    select 
                        'Proposta' end_point
                        ,concc030.id_documento IdProposta
                        ,corcc023.cd_inscricao_nacional CNPJCPF
                        ,corcc030.e_mail Email
                        ,concat('(55', isnull(ddd_telefone.ddd, ddd_endereco.ddd), corcc027.telefone, ')') Celular
                        ,cast(concc030.DH_Inclusao as date) dt_proposta
                        ,DH_Alteracao dt_ultimaModificacao
                        ,concc030.dt_adesao as dt_fechamento
                        ,cast(pagto_parc_1.dt_pagto_parcela_1 as date) dt_pagamento
                        ,case
                            when day(concc030.dt_adesao) < 12 then convert(varchar(7), concc030.dt_adesao, 120) + '-12'
                            when day(concc030.dt_adesao) = 12 then concc030.dt_adesao
                            else convert(varchar(7),dateadd(month, 1, concc030.dt_adesao), 120) + '-12'
                        end dt_validade
                        ,case
                            when concc030.st_situacao = 'C' then 'Cancelada'
                            when concc030.st_situacao = 'N' then 'Ativa'
                        end Status_proposta
                        ,cast(concc030.vl_bem as numeric(18,2)) Valor
                        ,concat(concc030.id_ponto_venda, '-', concc030.id_grupo_venda, '-', concc030.id_plano_venda, '-', concc030.id_bem) id_produto
                        ,case
                            when seguro.id_plano_seguro is not null then 'Sim'
                            else 'Não'
                        end as seguro
                        ,case
                when conve002.id_cota is not null then 'Assinado'
                else 'Não assinado'
            end status_contrato
                        ,cast(conve002d.dt_venda as date) dt_venda
                        ,concc030.id_ponto_venda
                        ,conve001.nm_fantasia ponto_venda
                        ,conve014.nm_fantasia vendedor
                    from
                        concc030
                        inner join
                            corcc023 on
                            corcc023.id_pessoa = concc030.id_pessoa
                        left join
                            concc036 on
                            concc036.id_empresa = concc030.id_empresa
                            and concc036.id_tipo_documento = concc030.id_tipo_documento
                            and concc036.id_documento = concc030.id_documento
                        left join
                            conve002 on
                            conve002.id_cota = concc036.id_cota
                        left join
                            conve002d on
                            conve002d.id_cota = conve002.id_cota
                        inner join
                                corcc026 on
                                corcc026.id_pessoa = concc030.id_pessoa
                                and corcc026.id_endereco = concc030.id_endereco
                        inner join
                            corcc027 on
                            corcc027.id_pessoa = concc030.id_pessoa
                            and corcc027.id_telefone = concc030.id_telefone
                        inner join
                            corcc015 ddd_endereco on
                            ddd_endereco.id_cidade = corcc026.id_cidade
                        inner join
                            corcc015 ddd_telefone on
                            ddd_telefone.id_cidade = corcc027.id_cidade
                        left join
                            (
                                select
                                    id_cota
                                    ,min(confi005.dt_pagamento) dt_pagto_parcela_1
                                from
                                    confi005c
                                    inner join
                                        confi005 on
                                        confi005.id_movimento_grupo = confi005c.id_movimento_grupo
                                where
                                    confi005.id_cd_movto_fin = 10 and confi005.id_movimento_estorno = 0
                                group by id_cota
                            ) pagto_parc_1 on
                            pagto_parc_1.id_cota = conve002.id_cota
                        left join
                            corcc030 on
                            corcc030.id_pessoa = concc030.id_pessoa
                            and corcc030.id_e_mail = concc030.id_e_mail
                        outer apply dbo.fn_dsvepeseguro(conve002.id_cota, getdate(), 10, 1) as seguro
                        inner join
                            conve001 on
                            conve001.id_ponto_venda = concc030.id_ponto_venda
                        inner join
                            conve014 on
                            conve014.id_comissionado = concc030.id_comissionado

            inner join 
                vw_GrProduto on 
                vw_GrProduto.id_grupo = concc030.id_grupo_venda
                and vw_GrProduto.ID_Plano_Venda = concc030.ID_Plano_Venda
                and vw_GrProduto.ID_Taxa_Plano = concc030.ID_Taxa_Plano
            --    and vw_GrProduto. = concc030.id_bem

            Where --concc030.id_ponto_venda = 259
            --and pagto_parc_1.dt_pagto_parcela_1 is null 
            --and concc030.st_situacao = 'N'
            cast(concc030.dt_adesao as date) >= CASE WHEN DAY(GETDATE()) >= 13 
                THEN DATEFROMPARTS(YEAR(GETDATE()), MONTH(GETDATE()), 13)
                ELSE DATEFROMPARTS(YEAR(GETDATE()), MONTH(GETDATE()) - 1, 13)
            END
            --and vw_GrProduto.ID_Segmento_Bem in (3,4)
            --and conve002.id_cota is null
        """
        # Adiciona LIMIT se test_sample estiver definido
        if self.test_sample:
            query += f"\nORDER BY concc030.id_documento\nOFFSET 0 ROWS FETCH NEXT {self.test_sample} ROWS ONLY"

        try:
            with get_database_connection(self.db_name) as conn:
                df = pd.read_sql(query, conn)
                
                # Processa dados
                df['IdProposta'] = df['IdProposta'].astype(str)
                
                # Validação de qualidade
                self._validate_proposal_data(df)
                
                self.logger.info(f"✅ {len(df)} propostas extraídas do NewCon")
                return df
                
        except Exception as e:
            self.logger.error(f"❌ Erro ao extrair propostas do NewCon: {e}")
            raise
    
    def _validate_client_data(self, df: pd.DataFrame):
        """Valida dados de clientes"""
        checker = DataQualityChecker()
        
        # Verifica campos obrigatórios
        required_fields = ['CNPJCPF', 'e_mail']
        for _, row in df.iterrows():
            is_valid, missing = checker.check_required_fields([row.to_dict()], required_fields)
            if not is_valid:
                self.logger.warning(f"Cliente com campos obrigatórios ausentes: {missing}")
    
    def _validate_lead_data(self, df: pd.DataFrame):
        """Valida dados de leads"""
        checker = DataQualityChecker()
        
        # Verifica campos obrigatórios
        required_fields = ['CNPJCPF', 'Dt_simulacao']
        for _, row in df.iterrows():
            is_valid, missing = checker.check_required_fields([row.to_dict()], required_fields)
            if not is_valid:
                self.logger.warning(f"Lead com campos obrigatórios ausentes: {missing}")
    
    def _validate_product_data(self, df: pd.DataFrame):
        """Valida dados de produtos"""
        # Verifica se há produtos sem ID
        if df['id_produto'].isna().any():
            self.logger.warning("Produtos sem ID encontrados")
    
    def _validate_proposal_data(self, df: pd.DataFrame):
        """Valida dados de propostas"""
        checker = DataQualityChecker()
        
        # Verifica campos obrigatórios
        required_fields = ['IdProposta', 'Email']
        for _, row in df.iterrows():
            is_valid, missing = checker.check_required_fields([row.to_dict()], required_fields)
            if not is_valid:
                self.logger.warning(f"Proposta com campos obrigatórios ausentes: {missing}")

# =============================================================================
# EXTRAÇÃO RD STATION (API)
# =============================================================================

class RDStationExtractor:
    """Extrator especializado para RD Station"""
    
    def __init__(self, test_sample: int = None):
        self.logger = logging.getLogger(__name__)
        self.test_sample = test_sample
        self.client = CRMClient(RDSTATION_CONFIG['token'])
        self.rate_limiter = RateLimiter(
            requests_per_minute=RDSTATION_CONFIG['rate_limit'],
            requests_per_second=RDSTATION_CONFIG['rate_limit_per_second']
        )
        
        # Mapeamento de campos customizados
        self.contact_fields = {
            '645578c328054b000f1b92cb': 'Estado',
            '6458fe2dfbe3dd0010a9f827': 'CPF',
            '6458feb9f63eb100129aa839': 'Gênero',
            '6458fed9328d400012d16444': 'Data de Nascimento',
            '6458ffa2edc3a90012b9a746': 'CEP',
            '645aa3276f5a2d000f32833d': 'Formulário de Simulacao',
            '645aa33f841efb00114c456f': 'O quanto voce conhece sobre consorcio?',
            '645aa35f20053c000f868796': 'Em quanto tempo pretende conquistar?',
            '67d4177b9d1a1a00147a4138': 'Telefone',
            '643fff689586e30019910a55': 'ValorSimulacao',
            '685b0396ed040b0019bf4c05': 'Valor do Bem',
            '685d6049dfc06d001edb357a': 'Cidade',
        }
        
        self.deal_fields = {
            '645cf919a66d5900118aa142': 'UTM_Source',
            '685d500574f982001ad79e8e': 'UTM_Medium',
            '685d500f9eacf0001af3d988': 'UTM_Term',
            '685d5022c9346f001e54cce7': 'UTM_Content',
            '6439494394d94a000dece78f': 'ID_Campanha',
            '685d505133d4ac001f45b475': 'Ponto_de_Venda',
            '685d612444467e001b80e87a': 'Plano_de_venda',
            '685d5af98c332d0014329beb': 'Valor_do_bem_deal',
            '643fff20fa5355001200c1d1': 'Simulacao_em_Parcela',
            '6671d5da10015c0023958b63': 'CPF_CNPJ_Deal',
        }
    
    @retry_decorator(max_attempts=3, delay=5)
    def extract_leads(self) -> pd.DataFrame:
        """Extrai leads do RD Station com múltiplas estratégias para garantir extração completa"""
        self.logger.info("Extraindo leads do RD Station...")

        # Primeiro carrega mapeamento de deals para vendedores
        deals_users_map, deals_data_map = self._load_deals_mapping()

        # ESTRATÉGIA OTIMIZADA: Extrai apenas os registros mais recentes (9.800 máximo)
        self.logger.info("🚀 Extraindo registros mais recentes do RD Station (ordenação decrescente por ID)")
        self.logger.info("📊 Limite máximo: 9.800 registros (49 páginas × 200 registros)")

        final_contacts = self._extract_most_recent_contacts(deals_users_map, deals_data_map)
        
        # Converte para DataFrame
        df = pd.DataFrame(final_contacts)

        # Deduplicação por ID do contato (evita duplicatas entre períodos subdivididos)
        if not df.empty and 'contact_id' in df.columns:
            initial_count = len(df)
            df = df.drop_duplicates(subset=['contact_id'], keep='first')
            final_count = len(df)
            if initial_count != final_count:
                self.logger.info(f"🔄 Deduplicação: {initial_count - final_count} duplicatas removidas ({final_count} únicos)")

        # Validação de qualidade
        self._validate_rdstation_data(df)
        
        # Validação de completeness
        self._validate_extraction_completeness(len(final_contacts))

        self.logger.info(f"✅ {len(df)} leads únicos extraídos do RD Station")
        return df

    def _extract_with_smart_pagination(self, deals_users_map: dict, deals_data_map: dict) -> List[dict]:
        """Extrai contatos usando paginação inteligente com ordenação por ID para contornar limite de 10.000"""
        self.logger.info("Usando estratégia de paginação inteligente com ordenação...")

        all_contacts = []
        page = 1
        max_safe_pages = RDSTATION_CONFIG['max_offset_limit'] // RDSTATION_CONFIG['page_size']

        # Ajusta max_pages se test_sample estiver definido
        if self.test_sample:
            max_safe_pages = min(max_safe_pages, (self.test_sample // RDSTATION_CONFIG['page_size']) + 1)

        tracker = ProgressTracker(0, "Extraindo leads RD Station com paginação inteligente")

        # Estratégia: usar ordenação por ID para garantir consistência
        order_field = 'id'  # Ordena por ID para consistência
        direction = 'asc'   # Crescente para pegar do mais antigo ao mais novo

        self.logger.info(f"Configuração: ordenação por {order_field} ({direction}), limite seguro de {max_safe_pages} páginas")

        while page <= max_safe_pages:
            try:
                # Rate limiting
                self.rate_limiter.wait_if_needed()

                # Calcula offset atual
                current_offset = (page - 1) * RDSTATION_CONFIG['page_size']
                if current_offset >= RDSTATION_CONFIG['max_offset_limit']:
                    self.logger.warning(f"⚠️ Atingido limite seguro de offset ({current_offset})")
                    self.logger.warning("💡 Para extrair mais dados, considere usar múltiplas execuções com filtros diferentes")
                    break

                self.logger.debug(f"Buscando página {page} (offset: {current_offset})...")

                # Faz chamada com ordenação
                response = self.client.list_contacts(
                    page=page,
                    limit=RDSTATION_CONFIG['page_size'],
                    order=order_field,
                    direction=direction
                )

                if not isinstance(response, dict):
                    self.logger.error(f"Resposta inválida da API: {response}")
                    break

                # Extrai contatos da resposta
                contacts = self._extract_contacts_from_response(response)

                if not contacts:
                    self.logger.info("Nenhum contato encontrado, finalizando...")
                    break

                # Processa contatos
                page_contacts = []
                for contact in contacts:
                    processed_contact = self._process_contact(
                        contact, deals_users_map, deals_data_map
                    )
                    page_contacts.append(processed_contact)

                all_contacts.extend(page_contacts)
                tracker.update(len(page_contacts))

                self.logger.info(f"✅ Página {page}: {len(page_contacts)} contatos processados")

                # Para se atingir test_sample
                if self.test_sample and len(all_contacts) >= self.test_sample:
                    self.logger.info(f"Limite de amostra atingido: {self.test_sample}")
                    break

                # Verifica se há mais páginas
                if len(contacts) < RDSTATION_CONFIG['page_size']:
                    self.logger.info("Última página alcançada (menos contatos que o limite)")
                    break

                page += 1

            except Exception as e:
                error_msg = str(e)
                if "Result window is too large" in error_msg:
                    self.logger.error(f"❌ Limite de 10.000 registros atingido na página {page}")
                    self.logger.error("💡 Extraídos todos os contatos possíveis com a limitação da API")
                    break
                elif "rate limit" in error_msg.lower():
                    self.logger.info("Rate limit atingido, aguardando 60 segundos...")
                    time.sleep(60)
                    continue
                else:
                    self.logger.error(f"Erro na página {page}: {e}")
                    break

        tracker.finish()

        # Log final com estatísticas
        self.logger.info(f"📊 Extração concluída:")
        self.logger.info(f"   - Páginas processadas: {page - 1}")
        self.logger.info(f"   - Contatos extraídos: {len(all_contacts)}")
        self.logger.info(f"   - Offset final: {(page - 1) * RDSTATION_CONFIG['page_size']}")

        if len(all_contacts) >= RDSTATION_CONFIG['max_offset_limit'] - RDSTATION_CONFIG['page_size']:
            self.logger.warning("⚠️ Possível limitação da API - pode haver mais contatos não extraídos")
            self.logger.info("💡 Para extrair todos os dados, considere:")
            self.logger.info("   1. Executar múltiplas vezes com filtros diferentes")
            self.logger.info("   2. Usar ordenação decrescente em execução separada")
            self.logger.info("   3. Filtrar por email ou query específica")

        return all_contacts

    def _extract_most_recent_contacts(self, deals_users_map: dict, deals_data_map: dict) -> List[dict]:
        """Extrai os registros mais recentes usando ordenação decrescente por ID"""
        self.logger.info("📋 Usando ordenação decrescente por ID para obter registros mais recentes")

        all_contacts = []
        page = 1
        max_safe_pages = RDSTATION_CONFIG['max_offset_limit'] // RDSTATION_CONFIG['page_size']

        # Ajusta max_pages se test_sample estiver definido
        if self.test_sample:
            max_safe_pages = min(max_safe_pages, (self.test_sample // RDSTATION_CONFIG['page_size']) + 1)

        tracker = ProgressTracker(0, "Extraindo registros mais recentes do RD Station")

        self.logger.info(f"🎯 Configuração: ordenação por ID (desc), máximo {max_safe_pages} páginas")

        while page <= max_safe_pages:
            try:
                # Rate limiting
                self.rate_limiter.wait_if_needed()

                # Calcula offset atual
                current_offset = (page - 1) * RDSTATION_CONFIG['page_size']
                if current_offset >= RDSTATION_CONFIG['max_offset_limit']:
                    self.logger.warning(f"⚠️ Atingido limite seguro de offset ({current_offset})")
                    self.logger.info("✅ Extração concluída - limite de API respeitado")
                    break

                self.logger.debug(f"Buscando página {page} (offset: {current_offset})...")

                # Faz chamada com ordenação decrescente por ID (mais recentes primeiro)
                response = self.client.list_contacts(
                    page=page,
                    limit=RDSTATION_CONFIG['page_size'],
                    order='id',
                    direction='desc'
                )

                if not isinstance(response, dict):
                    self.logger.error(f"Resposta inválida da API: {response}")
                    break

                # Extrai contatos da resposta
                contacts = self._extract_contacts_from_response(response)

                if not contacts:
                    self.logger.info("Nenhum contato encontrado, finalizando...")
                    break

                # Processa contatos
                page_contacts = []
                for contact in contacts:
                    processed_contact = self._process_contact(
                        contact, deals_users_map, deals_data_map
                    )
                    page_contacts.append(processed_contact)

                all_contacts.extend(page_contacts)
                tracker.update(len(page_contacts))

                self.logger.info(f"✅ Página {page}: {len(page_contacts)} contatos processados (total: {len(all_contacts)})")

                # Para se atingir test_sample
                if self.test_sample and len(all_contacts) >= self.test_sample:
                    self.logger.info(f"Limite de amostra atingido: {self.test_sample}")
                    break

                # Verifica se há mais páginas
                if len(contacts) < RDSTATION_CONFIG['page_size']:
                    self.logger.info("Última página alcançada (menos contatos que o limite)")
                    break

                page += 1

            except Exception as e:
                error_msg = str(e)
                if "Result window is too large" in error_msg:
                    self.logger.error(f"❌ Limite de 10.000 registros atingido na página {page}")
                    self.logger.info("✅ Extraídos todos os contatos possíveis com a limitação da API")
                    break
                elif "rate limit" in error_msg.lower():
                    self.logger.info("Rate limit atingido, aguardando 60 segundos...")
                    time.sleep(60)
                    continue
                else:
                    self.logger.error(f"Erro na página {page}: {e}")
                    break

        tracker.finish()

        self.logger.info(f"📊 Extração de registros mais recentes concluída:")
        self.logger.info(f"   - Páginas processadas: {page - 1}")
        self.logger.info(f"   - Contatos extraídos: {len(all_contacts)}")
        self.logger.info(f"   - Estratégia: ID decrescente (mais recentes primeiro)")

        return all_contacts

    def _load_deals_mapping(self) -> tuple:
        """Carrega mapeamento de deals para vendedores e dados"""
        self.logger.info("Carregando mapeamento de deals...")
        
        deals_users_map = {}
        deals_data_map = {}
        
        try:
            page = 1
            max_pages = 100  # Limite de segurança
            
            while page <= max_pages:
                # Rate limiting
                self.rate_limiter.wait_if_needed()
                
                self.logger.info(f"Carregando deals página {page}...")
                
                response = self.client.list_opportunities(page=page, limit=200)
                
                if not isinstance(response, dict) or 'deals' not in response:
                    break
                
                deals = response['deals']
                if not deals:
                    break
                
                for deal in deals:
                    deal_id = deal.get('id')
                    if deal_id:
                        # Mapeia vendedor
                        user_info = deal.get('user', {})
                        if user_info and isinstance(user_info, dict):
                            user_name = user_info.get('name')
                            if user_name:
                                deals_users_map[deal_id] = user_name
                        
                        # Armazena dados completos do deal
                        deals_data_map[deal_id] = deal
                
                if len(deals) < 200:
                    break
                
                page += 1
            
            self.logger.info(f"✅ Carregado mapeamento de {len(deals_users_map)} deals")
            
        except Exception as e:
            self.logger.warning(f"Erro ao carregar deals: {e}")
        
        return deals_users_map, deals_data_map
    
    def _extract_contacts_from_response(self, response: dict) -> List[dict]:
        """Extrai contatos da resposta da API"""
        contacts = None
        
        if 'contacts' in response:
            contacts = response['contacts']
        elif 'data' in response:
            if isinstance(response['data'], dict) and 'contacts' in response['data']:
                contacts = response['data']['contacts']
            elif isinstance(response['data'], list):
                contacts = response['data']
        
        return contacts or []
    
    def _process_contact(self, contact: dict, deals_users_map: dict, deals_data_map: dict) -> dict:
        """Processa um contato individual"""
        
        # Informações básicas
        contact_id = contact.get('id', '')
        created_at = self._safe_get_date(contact.get('created_at', ''))
        
        # Email
        email = ""
        emails = contact.get('emails', [])
        if emails and isinstance(emails[0], dict):
            email = emails[0].get('email', '')
        
        # Telefone
        phone = ""
        phones = contact.get('phones', [])
        if phones and isinstance(phones[0], dict):
            phone = phones[0].get('phone', '')
        
        # Nome
        full_name = contact.get('name', '')
        first_name, processed_full_name = self._safe_get_name_parts(full_name)
        
        # Campos customizados
        custom_fields = contact.get('contact_custom_fields', [])
        
        # Extrai campos customizados
        extracted_fields = {}
        for field in custom_fields:
            if isinstance(field, dict):
                field_id = field.get('custom_field_id')
                if field_id in self.contact_fields:
                    field_name = self.contact_fields[field_id]
                    extracted_fields[field_name] = str(field.get('value', '') or '')
        
        # Extrai dados de deals
        deal_fields = self._extract_deal_fields(contact, deals_data_map)
        
        # Obtém vendedor
        vendedor = self._get_responsible_user_name(contact, deals_users_map)
        
        # Monta registro
        return {
            'contact_id': contact_id,  # Adiciona ID para deduplicação
            'CNPJCPF': extracted_fields.get('CPF', ''),
            'Email': email,
            'Dt_cadastro': created_at,
            'Celular': phone,
            'Locale': 'br',
            'PrimeiroNome': first_name,
            'NomeCompleto': processed_full_name,
            'Cidade': extracted_fields.get('Cidade', ''),
            'Estado': extracted_fields.get('Estado', ''),
            'TipoEmpresa': 'Bamaq Consórcio',
            'TipoBem': 'Carta de Crédito',
            'TipoSimulacao': 'Consórcio',
            'ValorSimulacao': extracted_fields.get('ValorSimulacao', ''),
            'Campaign': deal_fields.get('Campaign', ''),
            'Source': deal_fields.get('Source', ''),
            'Medium': deal_fields.get('Medium', ''),
            'Term': deal_fields.get('Term', ''),
            'Content': deal_fields.get('Content', ''),
            'PontoVenda': deal_fields.get('PontoVenda', ''),
            'PlanoVenda': deal_fields.get('PlanoVenda', ''),
            'dt_nascimento': self._format_birth_date(extracted_fields.get('Data de Nascimento', '')),
            'Optin_consorcio_email': 'True',
            'Optin_consorcio_SMS': 'True',
            'Optin_consorcio_whatsapp': 'True',
            'Optin_seguros_email': '',
            'Optin_seguros_SMS': '',
            'Optin_seguros_wpp': '',
            'Optin_digital_email': '',
            'Optin_digital_SMS': '',
            'Optin_digital_whatsapp': '',
            'Optin_capital_email': '',
            'Optin_capital_SMS': '',
            'Optin_capital_whatsapp': '',
            'Dt_simulacao': created_at,
            'Nome vendedor': vendedor,
        }
    
    def _safe_get_date(self, date_str: str) -> str:
        """Extrai data de string ISO"""
        if not date_str:
            return ""
        try:
            return date_str.split('T')[0]
        except:
            return str(date_str)
    
    def _safe_get_name_parts(self, full_name: str) -> tuple:
        """Extrai primeiro nome e nome completo"""
        if not full_name:
            return "", ""
        
        # Remove vírgula inicial
        full_name = full_name.strip().lstrip(',').strip()
        
        if not full_name:
            return "", ""
        
        parts = full_name.split()
        if parts:
            first_name = parts[0]
            return first_name, full_name
        
        return "", full_name
    
    def _extract_deal_fields(self, contact: dict, deals_data_map: dict) -> dict:
        """Extrai campos de marketing/vendas dos deals"""
        deal_fields = {
            'Campaign': '', 'Source': '', 'Medium': '', 'Term': '', 'Content': '',
            'PontoVenda': '', 'PlanoVenda': '', 'ValorSimulacaoDeal': ''
        }
        
        deals = contact.get('deals', [])
        if deals and deals_data_map:
            for deal in deals:
                deal_id = deal.get('id') or deal.get('_id')
                if deal_id and deal_id in deals_data_map:
                    deal_data = deals_data_map[deal_id]
                    deal_custom_fields = deal_data.get('deal_custom_fields', [])
                    
                    # Extrai campos customizados do deal
                    for field in deal_custom_fields:
                        if isinstance(field, dict):
                            field_id = field.get('custom_field_id')
                            if field_id in self.deal_fields:
                                field_name = self.deal_fields[field_id]
                                value = str(field.get('value', '') or '')
                                
                                # Mapeia para campos finais
                                if field_name == 'ID_Campanha' and value and not deal_fields['Campaign']:
                                    deal_fields['Campaign'] = value
                                elif field_name == 'UTM_Source' and value and not deal_fields['Source']:
                                    deal_fields['Source'] = value
                                elif field_name == 'UTM_Medium' and value and not deal_fields['Medium']:
                                    deal_fields['Medium'] = value
                                elif field_name == 'UTM_Term' and value and not deal_fields['Term']:
                                    deal_fields['Term'] = value
                                elif field_name == 'UTM_Content' and value and not deal_fields['Content']:
                                    deal_fields['Content'] = value
                                elif field_name == 'Ponto_de_Venda' and value and not deal_fields['PontoVenda']:
                                    deal_fields['PontoVenda'] = value
                                elif field_name == 'Plano_de_venda' and value and not deal_fields['PlanoVenda']:
                                    deal_fields['PlanoVenda'] = value
        
        return deal_fields
    
    def _get_responsible_user_name(self, contact: dict, deals_users_map: dict) -> str:
        """Extrai nome do vendedor responsável"""
        if not deals_users_map:
            return ""
        
        deals = contact.get('deals', [])
        if not deals:
            return ""
        
        for deal in deals:
            if isinstance(deal, dict):
                deal_id = deal.get('id') or deal.get('_id')
                if deal_id and deal_id in deals_users_map:
                    return deals_users_map[deal_id]
            elif isinstance(deal, str):
                if deal in deals_users_map:
                    return deals_users_map[deal]
        
        return ""
    
    def _format_birth_date(self, date_str: str) -> str:
        """Converte DD/MM/YYYY para YYYY-MM-DD"""
        if not date_str or '/' not in date_str:
            return date_str
        
        try:
            parts = date_str.split('/')
            if len(parts) == 3:
                day, month, year = parts
                return f"{year}-{month.zfill(2)}-{day.zfill(2)}"
        except:
            pass
        
        return date_str
    
    def _validate_rdstation_data(self, df: pd.DataFrame):
        """Valida dados do RD Station"""
        if df.empty:
            self.logger.warning("Nenhum dado extraído do RD Station")
            return
        
        # Estatísticas de qualidade
        total_records = len(df)
        cpf_count = df['CNPJCPF'].notna().sum()
        email_count = df['Email'].notna().sum()
        phone_count = df['Celular'].notna().sum()
        
        self.logger.info(f"Qualidade dos dados RD Station:")
        self.logger.info(f"- Total de registros: {total_records}")
        self.logger.info(f"- CPF preenchido: {cpf_count} ({cpf_count/total_records*100:.1f}%)")
        self.logger.info(f"- Email preenchido: {email_count} ({email_count/total_records*100:.1f}%)")
        self.logger.info(f"- Telefone preenchido: {phone_count} ({phone_count/total_records*100:.1f}%)")

    def _validate_extraction_completeness(self, extracted_count: int):
        """Valida se a extração foi completa baseada no número de registros extraídos"""
        max_api_limit = 10000
        safe_limit = RDSTATION_CONFIG['max_offset_limit']
        
        if extracted_count >= safe_limit:
            self.logger.warning(f"⚠️ ALERTA: Extraídos {extracted_count} registros, próximo ao limite da API")
            self.logger.warning(f"📊 Limite seguro configurado: {safe_limit}")
            self.logger.warning(f"📊 Limite máximo da API: {max_api_limit}")
            
            # Calcula estimativa de dados perdidos
            estimated_missing = max_api_limit - extracted_count
            if estimated_missing > 0:
                self.logger.warning(f"📊 Possíveis registros não extraídos: ~{estimated_missing}")
                
            self.logger.info("🔄 Estratégia de extração múltipla implementada para contornar limitações")
            self.logger.info("✅ Recomendação: Executar múltiplas extrações com filtros diferentes se necessário")
            
        else:
            self.logger.info(f"✅ Extração completa: {extracted_count} registros (abaixo do limite de {safe_limit})")
            
        # Adiciona checkpoint para monitoramento
        if extracted_count > 0:
            coverage_percentage = min(100, (extracted_count / max_api_limit) * 100)
            self.logger.info(f"📊 Cobertura estimada: {coverage_percentage:.1f}% dos dados disponíveis")

# =============================================================================
# EXTRAÇÃO ORBBITS (MYSQL)
# =============================================================================

class OrbbitsExtractor:
    """Extrator especializado para Orbbits"""
    
    def __init__(self, test_sample: int = None):
        self.logger = logging.getLogger(__name__)
        self.db_name = 'orbbits'
        self.test_sample = test_sample
    
    @retry_decorator(max_attempts=3, delay=2)
    def extract_origin(self) -> pd.DataFrame:
        """Extrai dados de origem do Orbbits"""
        self.logger.info("Extraindo dados de origem do Orbbits...")
        query = """
        SELECT
            contractnumber,
            origin,
            lgpd
        FROM orbbits_partners.origin_search
        WHERE contractnumber IS NOT NULL
        """
        if self.test_sample:
            query += f"\nORDER BY contractnumber\nLIMIT {self.test_sample}"
        try:
            self.logger.info("[Orbbits][origin] Abrindo conexão com o banco...")
            with get_database_connection(self.db_name) as conn:
                self.logger.info("[Orbbits][origin] Conexão aberta com sucesso.")
                self.logger.info("[Orbbits][origin] Executando pd.read_sql...")
                df = pd.read_sql(query, conn)
                self.logger.info(f"[Orbbits][origin] pd.read_sql executado. Registros extraídos: {len(df)}")
                df['contractnumber'] = df['contractnumber'].astype(str)
                self.logger.info(f"✅ {len(df)} registros de origem extraídos do Orbbits")
                return df
        except Exception as e:
            self.logger.error(f"❌ Erro ao extrair origem do Orbbits: {e}")
            raise
    
    @retry_decorator(max_attempts=3, delay=2)
    def extract_payments(self) -> pd.DataFrame:
        """Extrai dados de pagamentos do Orbbits"""
        self.logger.info("Extraindo dados de pagamentos do Orbbits...")
        query = """
             SELECT distinct
            p.newcon_proposal_contract_number,
                (select payment_link from orbbits_charges.payments where newcon_proposal_contract_number = p.newcon_proposal_contract_number and type = 'bill' order by id desc limit 1 ) as payment_link_bill,
                (select payment_link from orbbits_charges.payments where newcon_proposal_contract_number = p.newcon_proposal_contract_number and type = 'credit_card' order by id desc limit 1 ) as payment_link_credit_card,
                (select payment_link from orbbits_charges.payments where newcon_proposal_contract_number = p.newcon_proposal_contract_number and type = 'pix' order by id desc  limit 1) as payment_link_pix,
                (select payment_date from orbbits_charges.payments where newcon_proposal_contract_number = p.newcon_proposal_contract_number and status = 'paid' order by id desc  limit 1) as payment_date,
                (select type from orbbits_charges.payments where newcon_proposal_contract_number = p.newcon_proposal_contract_number and status = 'paid' and payment_date  is not null order by id desc  limit 1) as type
            FROM orbbits_charges.payments p
        """
        if self.test_sample:
            query += f"\nORDER BY newcon_proposal_contract_number\nLIMIT {self.test_sample}"
        try:
            self.logger.info("[Orbbits][payments] Abrindo conexão com o banco...")
            try:
                with get_database_connection(self.db_name) as conn:
                    self.logger.info("[Orbbits][payments] Conexão aberta com sucesso.")
                    self.logger.info("[Orbbits][payments] Executando pd.read_sql...")                    
                    df = pd.read_sql(query, conn)
                    self.logger.info(f"[Orbbits][payments] pd.read_sql executado. Registros extraídos: {len(df)}")
                    df['newcon_proposal_contract_number'] = df['newcon_proposal_contract_number'].astype(str)
                    self.logger.info(f"✅ {len(df)} registros de pagamentos extraídos do Orbbits")
                    return df
            except Exception as conn_exc:
                self.logger.error(f"[Orbbits][payments] ERRO ao abrir conexão: {conn_exc}")
                raise
        except Exception as e:
            self.logger.error(f"❌ Erro ao extrair pagamentos do Orbbits: {e}")
            raise
    
    @retry_decorator(max_attempts=3, delay=2)
    def extract_sales(self) -> pd.DataFrame:
        """Extrai dados de vendas do Orbbits"""
        self.logger.info("Extraindo dados de vendas do Orbbits...")
        query = """
            SELECT 
            p.newcon_id_proposal as proposal_id, 
            s.sale_coupon as sale_coupon
            from
            orbbits_leads.proposals p
            join orbbits_partners.sales s on
            s.proposal_id = p.id
            where  coalesce(s.sale_coupon,'') <> ''
            order by
            p.newcon_id_proposal  
        """
        if self.test_sample:
            query += f"\nORDER BY proposal_id\nLIMIT {self.test_sample}"
        try:
            self.logger.info("[Orbbits][sales] Abrindo conexão com o banco...")
            with get_database_connection(self.db_name) as conn:
                self.logger.info("[Orbbits][sales] Conexão aberta com sucesso.")
                self.logger.info("[Orbbits][sales] Executando pd.read_sql...")
                df = pd.read_sql(query, conn)
                self.logger.info(f"[Orbbits][sales] pd.read_sql executado. Registros extraídos: {len(df)}")
                df['proposal_id'] = df['proposal_id'].astype(str)
                self.logger.info(f"✅ {len(df)} registros de vendas extraídos do Orbbits")
                return df
        except Exception as e:
            self.logger.error(f"❌ Erro ao extrair vendas do Orbbits: {e}")
            raise
    
    @retry_decorator(max_attempts=3, delay=2)
    def extract_prices(self) -> pd.DataFrame:
        """Extrai dados de preços do Orbbits"""
        self.logger.info("Extraindo dados de preços do Orbbits...")
        query = """
        WITH primeira_parc AS (
            SELECT
                id_plano_de_venda,
                id_ponto_de_venda,
                id_bem,
                GROUP_CONCAT(CONCAT(parc_inic, '-', parc_final, ' = ', valor_parc_sem_seguro) SEPARATOR '; ') AS valor_primeira_parcela,
                GROUP_CONCAT(CONCAT(parc_inic, '-', parc_final, ' = ', valor_parc_com_seguro) SEPARATOR '; ') AS valor_primeira_parcela_c_seguro
            FROM orbbitsdb.lista_preco_progressao
            WHERE parc_inic = 1
            GROUP BY id_plano_de_venda, id_ponto_de_venda, id_bem
        ),
        demais_parc AS (
            SELECT
                id_plano_de_venda,
                id_ponto_de_venda,
                id_bem,
                GROUP_CONCAT(CONCAT(parc_inic, '-', parc_final, ' = ', valor_parc_sem_seguro) SEPARATOR '; ') AS valor_parcelas,
                GROUP_CONCAT(CONCAT(parc_inic, '-', parc_final, ' = ', valor_parc_com_seguro) SEPARATOR '; ') AS valor_parcelas_c_seguro
            FROM orbbitsdb.lista_preco_progressao
            WHERE parc_inic > 1
            GROUP BY id_plano_de_venda, id_ponto_de_venda, id_bem
        )
        SELECT
            primeira_parc.id_plano_de_venda,
            primeira_parc.id_ponto_de_venda,
            primeira_parc.id_bem,
            primeira_parc.valor_primeira_parcela,
            primeira_parc.valor_primeira_parcela_c_seguro,
            demais_parc.valor_parcelas,
            demais_parc.valor_parcelas_c_seguro
        FROM primeira_parc
        INNER JOIN demais_parc ON 
            demais_parc.id_plano_de_venda = primeira_parc.id_plano_de_venda
            AND demais_parc.id_ponto_de_venda = primeira_parc.id_ponto_de_venda
            AND demais_parc.id_bem = primeira_parc.id_bem
        """
        if self.test_sample:
            query += f"\nORDER BY primeira_parc.id_plano_de_venda\nLIMIT {self.test_sample}"
        try:
            self.logger.info("[Orbbits][prices] Abrindo conexão com o banco...")
            with get_database_connection(self.db_name) as conn:
                self.logger.info("[Orbbits][prices] Conexão aberta com sucesso.")
                self.logger.info("[Orbbits][prices] Executando pd.read_sql...")
                df = pd.read_sql(query, conn)
                self.logger.info(f"[Orbbits][prices] pd.read_sql executado. Registros extraídos: {len(df)}")
                self.logger.info(f"✅ {len(df)} registros de preços extraídos do Orbbits")
                return df
        except Exception as e:
            self.logger.error(f"❌ Erro ao extrair preços do Orbbits: {e}")
            raise

# =============================================================================
# RATE LIMITER
# =============================================================================

class RateLimiter:
    """Rate limiter para controlar frequência de requests"""
    
    def __init__(self, requests_per_minute: int, requests_per_second: int = None):
        self.requests_per_minute = requests_per_minute
        self.requests_per_second = requests_per_second or (requests_per_minute // 60)
        self.last_request_time = 0
        self.request_count = 0
        self.minute_start = time.time()
        self.lock = Lock()
    
    def wait_if_needed(self):
        """Aguarda se necessário para respeitar rate limit"""
        with self.lock:
            current_time = time.time()
            
            # Reset contador por minuto
            if current_time - self.minute_start >= 60:
                self.request_count = 0
                self.minute_start = current_time
            
            # Verifica limite por segundo
            if self.requests_per_second:
                time_since_last = current_time - self.last_request_time
                min_interval = 1.0 / self.requests_per_second
                
                if time_since_last < min_interval:
                    wait_time = min_interval - time_since_last
                    time.sleep(wait_time)
                    current_time = time.time()
            
            # Verifica limite por minuto
            if self.request_count >= self.requests_per_minute:
                wait_time = 60 - (current_time - self.minute_start)
                if wait_time > 0:
                    time.sleep(wait_time)
                    self.request_count = 0
                    self.minute_start = time.time()
            
            self.last_request_time = current_time
            self.request_count += 1

# =============================================================================
# FUNÇÕES PRINCIPAIS (PARA COMPATIBILIDADE)
# =============================================================================

def extract_all_sources(sources: List[str] = None) -> Dict[str, pd.DataFrame]:
    """Função principal para extrair dados de todas as fontes"""
    extractor = DataExtractor()
    return extractor.extract_all_sources(sources)

def extract_newcon_clients() -> pd.DataFrame:
    """Extrai clientes do NewCon"""
    extractor = NewConExtractor()
    return extractor.extract_clients()

def extract_newcon_leads() -> pd.DataFrame:
    """Extrai leads do NewCon"""
    extractor = NewConExtractor()
    return extractor.extract_leads()

def extract_newcon_products() -> pd.DataFrame:
    """Extrai produtos do NewCon"""
    extractor = NewConExtractor()
    return extractor.extract_products()

def extract_newcon_proposals() -> pd.DataFrame:
    """Extrai propostas do NewCon"""
    extractor = NewConExtractor()
    return extractor.extract_proposals()

def extract_rdstation_leads() -> pd.DataFrame:
    """Extrai leads do RD Station"""
    extractor = RDStationExtractor()
    return extractor.extract_leads()

def extract_orbbits_origin() -> pd.DataFrame:
    """Extrai dados de origem do Orbbits"""
    extractor = OrbbitsExtractor()
    return extractor.extract_origin()

def extract_orbbits_payments() -> pd.DataFrame:
    """Extrai dados de pagamentos do Orbbits"""
    extractor = OrbbitsExtractor()
    return extractor.extract_payments()

def extract_orbbits_sales() -> pd.DataFrame:
    """Extrai dados de vendas do Orbbits"""
    extractor = OrbbitsExtractor()
    return extractor.extract_sales()

def extract_orbbits_prices() -> pd.DataFrame:
    """Extrai dados de preços do Orbbits"""
    extractor = OrbbitsExtractor()
    return extractor.extract_prices()

# =============================================================================
# INSTANCIAÇÃO DAS CLASSES PRINCIPAIS
# =============================================================================

# Instâncias globais para uso direto
data_extractor = DataExtractor()
newcon_extractor = NewConExtractor()
rdstation_extractor = RDStationExtractor()
orbbits_extractor = OrbbitsExtractor()

# =============================================================================
# MAIN PARA TESTES
# =============================================================================

if __name__ == "__main__":
    # Testa extrações
    import sys
    #sys.path.append("/opt/airflow/dags/salesforce_integration")

    logging.basicConfig(level=logging.INFO)
    
    try:
        # Testa extração de todas as fontes
        print("Testando extração de todas as fontes...")
        
        results = extract_all_sources()
        
        print(f"\nResultados:")
        for source, df in results.items():
            if isinstance(df, pd.DataFrame):
                print(f"- {source}: {len(df)} registros")
                
                # Salva amostras para validação - MARCADAS COMO TESTE
                if len(df) > 0:
                    from test_data_manager import mark_as_test_data

                    # Marca amostra como dados de teste
                    sample_df = mark_as_test_data(
                        df.head(10),
                        description=f"Amostra de {source} para validação - NÃO USAR EM PRODUÇÃO",
                        source_module="data_extractors"
                    )

                    sample_file = f"TESTE_sample_{source}.csv"
                    sample_df.to_csv(sample_file, index=False)
                    print(f"  → Amostra de TESTE salva em {sample_file}")
                    print(f"    ⚠️  ATENÇÃO: Arquivo contém dados de TESTE, não usar em produção!")
        
        print("\n✅ Teste de extração concluído com sucesso!")
        
    except Exception as e:
        print(f"❌ Erro durante teste: {e}")
        sys.exit(1)