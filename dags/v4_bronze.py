"""
DAG V4 Bronze - Gerador Dinâmico de DAGs

Esta DAG gera automaticamente DAGs bronze para cada sistema configurado,
com processamento paralelo de tabelas e configurações específicas por sistema.

Sistemas Suportados:
- Syonet (SQL Server)
- Oracle ERP (Oracle) - futuro
- Sistema X (PostgreSQL) - futuro

Características:
- Processamento paralelo de tabelas
- Configurações específicas por sistema
- Validação automática
- Logs estruturados
- Reutilização total do framework
"""

from datetime import datetime, timedelta
from airflow import DAG
from airflow.operators.python import PythonOperator
from airflow.operators.dummy import DummyOperator
from airflow.utils.trigger_rule import TriggerRule
from typing import Dict, Any

# Imports do ETL Framework
from etl_framework.bronze.processor import create_bronze_processor
from etl_framework.config.dw_config import create_corporate_dw_config


# Configuração padrão das DAGs
default_args = {
    'owner': 'data-engineering',
    'depends_on_past': False,
    'start_date': datetime(2024, 1, 1),
    'email_on_failure': True,
    'email_on_retry': False,
    'retries': 2,
    'retry_delay': timedelta(minutes=5),
    'catchup': False
}

# Configuração dos sistemas
SYSTEMS_CONFIG = {
    'syonet': {
        'description': '🥉 BRONZE SYONET: Dados Brutos com Framework ETL V4 - Estratégias Inteligentes (Smart Incremental → Full Load)',
        'schedule_interval': None,  # Executada apenas via orquestração V4
        'max_active_runs': 1,
        'tags': ['syonet', 'bronze', 'framework-v4', 'etl']
    },
    # Futuro: Oracle ERP
    # 'oracle_erp': {
    #     'description': 'Sistema Oracle ERP - Financeiro (Oracle)',
    #     'schedule_interval': '0 7 * * *',  # 7h da manhã
    #     'max_active_runs': 1,
    #     'tags': ['oracle', 'bronze', 'erp', 'financeiro']
    # }
}


def create_process_table_function(system_name: str, table_name: str):
    """
    Cria função para processar uma tabela específica
    
    Args:
        system_name: Nome do sistema (syonet, oracle_erp, etc.)
        table_name: Nome da tabela
    """
    def process_table(**context):
        # Configuração única do DW corporativo
        dw_config = create_corporate_dw_config()
        
        # Cria processador bronze específico para o sistema
        processor = create_bronze_processor(system_name, dw_config)
        
        try:
            # Obtém configuração da tabela
            table_config = processor.system_config.get_bronze_table(table_name)
            if not table_config:
                raise Exception(f"Tabela {table_name} não encontrada na configuração do {system_name}")
            
            # Processa a tabela
            result = processor.process_single_table(table_config)
            
            if result['success']:
                print(f"✅ {table_name} processada com sucesso!")
                print(f"📊 Registros: {result['records_processed']}")
                print(f"🔄 Estratégia: {result['strategy_used']}")
                print(f"⏱️ Tempo: {result['execution_time']:.1f}s")
            else:
                raise Exception(f"Falha no processamento de {table_name}: {result.get('error')}")
            
            return result
            
        finally:
            processor.cleanup()
    
    return process_table


def create_validate_dependencies_function(system_name: str):
    """
    Cria função para validar dependências do sistema
    """
    def validate_dependencies(**context):
        # Configuração única do DW corporativo
        dw_config = create_corporate_dw_config()
        
        # Cria processador para validação
        processor = create_bronze_processor(system_name, dw_config)
        
        try:
            # Valida configuração do sistema
            print(f"🔍 Validando configuração do {system_name}...")
            
            # Testa conexão com sistema de origem
            print(f"🔗 Testando conexão com {system_name}...")
            processor.source_connection.test_connection()
            
            # Testa conexão com DW
            print("🔗 Testando conexão com DW...")
            processor.dw_connection.test_connection()
            
            print(f"✅ Dependências do {system_name} validadas com sucesso")
            return True
            
        finally:
            processor.cleanup()
    
    return validate_dependencies


def create_generate_report_function(system_name: str):
    """
    Cria função para gerar relatório final
    """
    def generate_report(**context):
        # Coleta resultados das tasks anteriores
        dag_run = context['dag_run']
        task_instances = dag_run.get_task_instances()

        successful_tables = []
        failed_tables = []
        total_records = 0
        total_time = 0

        for ti in task_instances:
            if ti.task_id.startswith('process_') and ti.state == 'success':
                try:
                    result = ti.xcom_pull()
                    if result and result.get('success'):
                        successful_tables.append(ti.task_id.replace('process_', ''))
                        total_records += result.get('records_processed', 0)
                        total_time += result.get('execution_time', 0)
                    else:
                        failed_tables.append(ti.task_id.replace('process_', ''))
                except:
                    failed_tables.append(ti.task_id.replace('process_', ''))
        
        # Gera relatório
        print(f"📊 RELATÓRIO FINAL - {system_name.upper()}")
        print("=" * 50)
        print(f"✅ Tabelas processadas com sucesso: {len(successful_tables)}")
        print(f"❌ Tabelas com falha: {len(failed_tables)}")
        print(f"📈 Total de registros processados: {total_records:,}")
        print(f"⏱️ Tempo total de processamento: {total_time:.1f}s")
        
        if successful_tables:
            print(f"\n✅ Sucessos: {', '.join(successful_tables)}")
        
        if failed_tables:
            print(f"\n❌ Falhas: {', '.join(failed_tables)}")
        
        print("=" * 50)
        
        return {
            'system': system_name,
            'successful_tables': successful_tables,
            'failed_tables': failed_tables,
            'total_records': total_records,
            'total_time': total_time
        }
    
    return generate_report


def create_bronze_dag(system_name: str, system_config: Dict[str, Any]) -> DAG:
    """
    Cria DAG bronze para um sistema específico
    
    Args:
        system_name: Nome do sistema
        system_config: Configuração do sistema
        
    Returns:
        DAG: DAG configurada para o sistema
    """
    
    dag_id = f'V4_BRONZE_{system_name.upper()}'
    
    dag = DAG(
        dag_id,
        default_args=default_args,
        description=system_config['description'],
        schedule_interval=system_config['schedule_interval'],
        catchup=False,
        max_active_runs=system_config['max_active_runs'],
        tags=system_config['tags']
    )
    
    # Task de início
    start_task = DummyOperator(
        task_id='start',
        dag=dag
    )
    
    # Task de validação de dependências
    validate_task = PythonOperator(
        task_id='validate_dependencies',
        python_callable=create_validate_dependencies_function(system_name),
        dag=dag
    )
    
    # Obtém lista de tabelas para processamento paralelo
    dw_config = create_corporate_dw_config()
    processor = create_bronze_processor(system_name, dw_config)
    bronze_tables = processor.get_bronze_tables_for_parallel_processing()
    processor.cleanup()
    
    # Cria tasks para cada tabela (processamento paralelo)
    table_tasks = []
    for table_config in bronze_tables:
        task = PythonOperator(
            task_id=f'process_{table_config.name}',
            python_callable=create_process_table_function(system_name, table_config.name),
            dag=dag
        )
        table_tasks.append(task)
    
    # Task de relatório final
    report_task = PythonOperator(
        task_id='generate_report',
        python_callable=create_generate_report_function(system_name),
        dag=dag
    )
    
    # Task de finalização bronze (como V3)
    bronze_complete = DummyOperator(
        task_id='bronze_processing_complete',
        dag=dag,
        trigger_rule=TriggerRule.NONE_FAILED_MIN_ONE_SUCCESS  # ✅ Mesma lógica do V3
    )

    # ===== DEPENDÊNCIAS - PARALELISMO TOTAL COMO V3 =====

    # PARALELIZAÇÃO TOTAL: Todas as tabelas executam em paralelo após validação
    # Removendo dependências artificiais para máximo paralelismo (como V3)
    start_task >> validate_task >> table_tasks

    # Todas as tabelas convergem para finalização bronze
    table_tasks >> bronze_complete >> report_task
    
    return dag


# Gera DAGs dinamicamente para cada sistema configurado
for system_name, system_config in SYSTEMS_CONFIG.items():
    try:
        # Cria DAG para o sistema
        dag = create_bronze_dag(system_name, system_config)
        
        # Adiciona ao namespace global para que o Airflow encontre
        globals()[f'V4_BRONZE_{system_name.upper()}'] = dag
        
        print(f"✅ DAG V4_BRONZE_{system_name.upper()} criada com sucesso")
        
    except Exception as e:
        print(f"❌ Erro ao criar DAG para {system_name}: {str(e)}")
        # Não falha a importação, apenas loga o erro
